<?php

namespace App\Models;

use App\Traits\HasUuid;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;

class NotificationTemplate extends Model implements TranslatableContract
{
    use HasUuid, Translatable;
    
    protected $fillable = ['key', 'type', 'is_active', 'placeholders'];
    public $translatedAttributes = ['title', 'message'];

}
