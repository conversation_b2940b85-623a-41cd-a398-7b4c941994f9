<?php

namespace App\Http\Controllers;

use App\Models\NotificationTemplate;
use App\Models\NotificationTemplateTranslation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NotificationTemplateController extends Controller
{
    public function index()
    {
        $templates = NotificationTemplate::with('translations')->get();
        return view('dashboard.notification-templates.index', compact('templates'));
    }

    public function create()
    {
        return view('dashboard.notification-templates.create');
    }

    public function store(Request $request)
    {
        // $request->validate([
        //     'key' => 'required|unique:notification_templates,key',
        //     'placeholder' => 'nullable|array',
        //     'placeholder.*' => 'string|regex:/^[a-z_]+$/',
        //     'translations.en.title' => 'required',
        //     'translations.en.message' => 'required',
        //     'translations.es.title' => 'required',
        //     'translations.es.message' => 'required',
        // ]);
        try{
            DB::beginTransaction();
            $request_data = $request->all();
    
            $template = NotificationTemplate::create($request_data);
            return $template;
    
            foreach ($request->translations as $locale => $translation) {
                NotificationTemplateTranslation::create([
                    'notification_template_id' => $template->id,
                    'locale' => $locale,
                    'title' => $translation['title'],
                    'message' => $translation['message']
                ]);
            }
    
            return redirect()->route('notification-templates.index')
                ->with('success', 'Notification template created successfully');

        }catch(\Exception $e){
            DB::rollBack();
            return $e->getMessage();
            return back()->with(["message" => $e->getMessage(), "type" => "error"]);
        }
    }

    public function edit($id)
    {
        $template = NotificationTemplate::with('translations')->findOrFail($id);
        return view('dashboard.notification-templates.edit', compact('template'));
    }

    public function update(Request $request, $id)
    {
        $template = NotificationTemplate::findOrFail($id);

        $request->validate([
            'placeholder' => 'nullable|array',
            'placeholder.*' => 'string|regex:/^[a-z_]+$/',
            'translations.en.title' => 'required',
            'translations.en.message' => 'required',
            'translations.es.title' => 'required',
            'translations.es.message' => 'required',
        ]);

        // Get placeholders from the new input format
        $placeholders = $request->input('placeholder', []);

        // Ensure uniqueness
        $placeholders = array_unique($placeholders);

        // Update template with new placeholders
        $template->update([
            'placeholders' => $placeholders
        ]);

        foreach ($request->translations as $locale => $translation) {
            NotificationTemplateTranslation::updateOrCreate(
                [
                    'notification_template_id' => $template->id,
                    'locale' => $locale
                ],
                [
                    'title' => $translation['title'],
                    'message' => $translation['message']
                ]
            );
        }

        return redirect()->route('notification-templates.index')
            ->with('success', 'Notification template updated successfully');
    }
        public function destroy($id)
        {
            $template = NotificationTemplate::findOrFail($id);
                $template->delete();
                 return redirect()->route('notification-templates.index')
            ->with('success', 'Notification Deleted successfully');
        }
}
